@echo off
REM Silent install of .NET Framework 3.5
dotnetfx35.exe /q /norestart

REM Wait for .NET install to finish before proceeding
if %errorlevel% neq 0 (
    echo .NET Framework installation failed with error %errorlevel%.
    exit /b %errorlevel%
)

REM Silent install of ReportViewer
ReportViewer.exe /quiet /norestart

if %errorlevel% neq 0 (
    echo ReportViewer installation failed with error %errorlevel%.
    exit /b %errorlevel%
)

echo Installation completed successfully.
exit /b 0
